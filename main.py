import os, textwrap
from dotenv import load_dotenv
from qdrant_client import QdrantClient
from qdrant_client.http import models
from openai import OpenAI
import chainlit as cl

load_dotenv()

COLLECTION = "docs"
EMBED_MODEL = "text-embedding-3-small"  # 1536-dim

qdrant = QdrantClient(
    url=os.environ["QDRANT_URL"],
    api_key=os.environ["QDRANT_API_KEY"],
)

if COLLECTION not in [c.name for c in qdrant.get_collections().collections]:
    qdrant.create_collection(
        collection_name=COLLECTION,
        vectors_config=models.VectorParams(size=1536, distance=models.Distance.COSINE),
    )

openai_client = OpenAI(api_key=os.environ["OPENAI_API_KEY"])

def embed(texts):
    res = openai_client.embeddings.create(model=EMBED_MODEL, input=texts)
    return [d.embedding for d in res.data]

@cl.on_chat_start
async def start():
    await cl.Message("Upload txt, md, or pdf, or just ask a question.").send()

@cl.on_message
async def route(msg: cl.Message):
    if msg.elements:  # file ingestion
        elem = msg.elements[0]
        raw = await elem.download()
        txt = raw.decode("utf-8", errors="ignore") if isinstance(raw, bytes) else raw
        chunks = textwrap.wrap(txt, 1000)
        vecs = embed(chunks)
        qdrant.upsert(
            collection_name=COLLECTION,
            wait=True,
            points=[
                models.PointStruct(id=i, vector=v, payload={"text": t})
                for i, (v, t) in enumerate(zip(vecs, chunks))
            ],
        )
        await cl.Message(f"Ingested {len(chunks)} chunks from {elem.name}.").send()
        return

    q_vec = embed([msg.content])[0]
    hits = qdrant.search(collection_name=COLLECTION, query_vector=q_vec, limit=5)
    context = "\n\n".join(h.payload["text"] for h in hits)
    prompt = f"Context:\n{context}\n\nQuestion: {msg.content}\nAnswer:"
    chat = openai_client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": prompt}],
    )
    await cl.Message(chat.choices[0].message.content).send()
